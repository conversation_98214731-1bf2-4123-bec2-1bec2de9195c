<template>
  <div>
    <!-- <el-button
      class="legend-img"
      style="background: rgba(0, 0, 0, 0.51)"
      size="small"
      circle
      @click="showLegend"
    >
      <img style="width: 18px" src="/images/图例sample.svg" />
    </el-button>
    -->

    <div class="legend-window" height="auto" v-show="show">
      <header class="su-panel-header">
        <div class="ellipse17"></div>
        <span>图例</span>
        <i
          v-show="mincloseShow"
          :class="['iconfont f18  icon-zuixiaohua minIcon']"
          @click="minWindow"
        >
        </i>
        <i
          :class="['iconfont f18  icon-chacha closeIcon']"
          @click="closeWindow"
        >
        </i>
      </header>
      <img
        width="200"
        class="mr25"
        src="/public/images/su-panel-title-bg.png"
      />
      <div class="legend-panel-body" v-show="bodyShow">
        <div v-for="item in legends">
          <div class="layer-header">
            <span style="font-size: 18px">{{ item.layerName }}</span>
            <div class="button-group">
              <el-button
                v-if="!isAllSelected(item.layerName)"
                type="text"
                size="small"
                @click="selectAll(item.layerName)"
              >
                全选
              </el-button>
              <el-button
                v-if="hasSelectedLegends(item.layerName)"
                type="text"
                size="small"
                @click="invertSelection(item.layerName)"
              >
                反选
              </el-button>
            </div>
          </div>
          <li
            v-for="secitem in item.legendsArr"
            style="list-style-type: none"
            class="legend-item"
            :class="{
              'legend-item-active':
                typeof secitem.isActive === 'undefined' || secitem.isActive,
            }"
            @click="toggleActive(secitem, $event)"
          >
            <div class="legend-image">
              <img :src="'data:image/png;base64,' + secitem.legendPNGBase64" />
            </div>
            <div class="legend-label">{{ secitem.label }}</div>
          </li>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, inject, watch, provide } from "vue";
import { useStore, mapGetters } from "vuex";
// import store from "@/store";
import legendStore from "@/store/legendStore.js";
import iserverMapLayer from "@/components/common/class/iserverMapLayer";
const mincloseShow = ref(true);
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  show: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "0",
  },
  width: {
    type: String,
    default: "340px",
  },
  height: {
    type: String,
    default: "auto",
  },
  borderRadius: {
    type: String,
    default: "0 0 1rem 0",
  },
  padding: {
    type: String,
    default: "0 15px 15px 15px",
  },
  data: {
    type: Object,
    default: {},
  },
});

const isShowLegendPanel = ref(false);
const bodyShow = ref(true);

const legendArr = ref([]);
const unshowField = ref({});
const store = useStore();
const showLegend = () => {
  isShowLegendPanel.value = !isShowLegendPanel.value;
};
const selectedLegends = ref({});
const legends = computed(() => {
  return store.getters.getLegends;
});
// 监听legends变化，初始化selectedLegends
watch(
  () => legends.value,
  (newLegends) => {
    newLegends.forEach((layer) => {
      if (!selectedLegends.value[layer.layerName]) {
        selectedLegends.value[layer.layerName] = [];
      }
    });
  },
  { immediate: true }
);

// 检查图层是否有选中的图例项
const hasSelectedLegends = (layerName) => {
  return selectedLegends.value[layerName]?.length > 0;
};

// 反选功能实现
const invertSelection = async (layerName) => {
  const layer = legends.value.find((l) => l.layerName === layerName);
  if (!layer) return;

  // 遍历图层的所有图例项，反转选中状态
  layer.legendsArr.forEach((item) => {
    if (typeof item.isActive === "undefined") {
      item.isActive = true; // 初始状态为显示
    }
    item.isActive = !item.isActive;
  });

  // 更新selectedLegends
  if (!selectedLegends.value[layerName]) {
    selectedLegends.value[layerName] = [];
  }

  // 清空当前选中数组，重新添加未显示的项
  selectedLegends.value[layerName] = layer.legendsArr
    .filter((item) => !item.isActive) // 只收集未激活的项
    .map((item) => item.unique);

  //获取是否全选
  hideIserverFeatureLayer(layerName);

  await handleShowIserverFeatureLayer(layerName);
};

const toggleActive = async (secitem, event) => {
  if (typeof secitem.isActive === "undefined") {
    secitem.isActive = true; // 初始状态应该是true（显示）
  }
  secitem.isActive = !secitem.isActive;

  // 获取当前点击的li元素的父级div
  const parentDiv = event.target.closest("li").parentElement;
  // 获取父级div中的span标签内容，即layerName
  const layerName = parentDiv.querySelector("span").textContent.trim();

  // 初始化selectedLegends
  if (!selectedLegends.value[layerName]) {
    selectedLegends.value[layerName] = [];
  }

  // 更新selectedLegends - 修正逻辑
  if (!secitem.isActive) {
    // 如果图例被禁用
    if (!selectedLegends.value[layerName].includes(secitem.unique)) {
      selectedLegends.value[layerName].push(secitem.unique); // 添加到未显示列表
    }
  } else {
    // 如果图例被启用
    const index = selectedLegends.value[layerName].indexOf(secitem.unique);
    if (index > -1) {
      selectedLegends.value[layerName].splice(index, 1); // 从未显示列表中移除
    }
  }

  //获取是否全选
  hideIserverFeatureLayer(layerName);

  await handleShowIserverFeatureLayer(layerName);
};

const hideIserverFeatureLayer = (layerName) => {
  const layer = legends.value.find((l) => l.layerName === layerName);
  let isAllSelected = layer.legendsArr.every(
    (item) => typeof item.isActive === "undefined" || item.isActive === true
  );
  if (isAllSelected) {
    iserverMapLayer.removeFeatureLayerByEntity(layerName);
    iserverMapLayer.show(iserverMapLayer.getLayerByName(layerName));
  }
  return isAllSelected;
};

const handleShowIserverFeatureLayer = async (layerName) => {
  const legendFeildName = store.state.layerFieldMap[layerName];

  let selectedLegendArr = selectedLegends.value[layerName];
  //如果不是反选
  if (selectedLegendArr && selectedLegendArr.length > 0) {
    let sql = legendFeildName + "";
    selectedLegendArr.map((item, index) => {
      if (index != selectedLegendArr.length - 1) {
        sql = sql + `!= '${item}'  and ${legendFeildName} `;
      } else {
        sql = sql + `!= '${item}'`;
      }
    });
    await iserverMapLayer.addFeatureLayerByEntity(layerName, sql);
    if (iserverMapLayer.getLayerByName(layerName).show) {
      iserverMapLayer.hide(iserverMapLayer.getLayerByName(layerName));
    }
  } else {
    iserverMapLayer.show(iserverMapLayer.getLayerByName(layerName));
  }
};

const emits = defineEmits(["closeToolWindow"]);
function closeWindow() {
  emits("closeToolWindow");
}
function minWindow() {
  bodyShow.value = !bodyShow.value;
}
// 检查是否全部选中
const isAllSelected = (layerName) => {
  const layer = legends.value.find((l) => l.layerName === layerName);
  if (!layer) {
    console.log("isAllSelected", layerName);
    return true;
  }
  let isAllSelected = layer.legendsArr.every(
    (item) => typeof item.isActive === "undefined" || item.isActive === true
  );
  if (isAllSelected) {
  }
  return isAllSelected;
};

// 全选功能实现
const selectAll = async (layerName) => {
  const layer = legends.value.find((l) => l.layerName === layerName);
  if (!layer) return;

  // 将所有图例项设置为激活状态
  layer.legendsArr.forEach((item) => {
    item.isActive = true;
  });

  // 清空selectedLegends中该图层的选择
  selectedLegends.value[layerName] = [];
  hideIserverFeatureLayer(layerName);
};
</script>

<style lang="scss">
.legend-img {
  // border-radius: 0 0 0 24px 0 0;
  position: fixed;
  z-index: 100;
  right: 2rem;
  bottom: 2rem;
}
.legend-window {
  background: rgba(16, 27, 55, 0.8);
  opacity: 0.8;
  border-radius: 15px !important;
  backdrop-filter: blur(15px);
  box-shadow: 10px 10px 30px 5px rgba(0, 0, 0, 0.15);
  position: absolute;
  right: 3.5rem;
  bottom: 3rem;
  width: 360px;
  padding: 0 15px 15px 15px;
  font-size: 14px;
  z-index: 100;
  /*
  &::before {
    content: "";
    display: block;
    height: 1px;
    width: 100%;
    background-image: linear-gradient(
      270deg,
      rgba(106, 251, 255, 0) 0%,
      #38f4ff 100%
    );
    position: absolute;
    top: 0;
    left: 0;
  }*/

  &-header {
    font-size: 18px;
    color: #ffffff;
    background-image: linear-gradient(270deg, #ffffff 0%, #a0deff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bold;
    color: transparent;
    display: flex;
    align-items: center;
    height: 50px;

    &::after {
      content: "";
      flex: 1;
      height: 20px;
      background-image: url(../../public/images/panel-title-icon2.svg);
      background-repeat: no-repeat;
      background-size: 60% 100%;
      margin-left: 10px;
    }
  }

  &-body {
    height: calc(100% - 50px);
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;

    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }

    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }

  .legend-panel-body {
    height: 250px;
    overflow: auto;
    margin-right: -7px;
    padding-right: 7px;

    .legend-item {
      cursor: pointer;
      padding: 8px 12px;
      margin: 5px 0;
      border-radius: 6px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;

      .legend-image {
        flex-shrink: 0;
        background: transparent;
        padding: 2px;
        border-radius: 4px;
        img {
          display: block;
        }
      }

      .legend-label {
        flex: 1;
        padding: 4px 8px;
        border-radius: 4px;
        background: rgba(13, 153, 255, 0.15);
        box-shadow: 0 0 8px rgba(13, 153, 255, 0.2);
        opacity: 0.6;
        transition: all 0.3s ease;
      }

      &:hover .legend-label {
        background: rgba(13, 153, 255, 0.35);
        box-shadow: 0 0 12px rgba(13, 153, 255, 0.4);
        opacity: 0.8;
      }

      &.legend-item-active .legend-label {
        background: rgba(13, 153, 255, 0.5);
        box-shadow: 0 0 15px rgba(13, 153, 255, 0.6);
        opacity: 1;
      }
    }
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 3px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 5px;
      scrollbar-arrow-color: rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
      background: rgba(0, 0, 0, 0);
      scrollbar-arrow-color: rgba(0, 0, 0, 0);
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
      border-radius: 0;
      background: rgba(0, 0, 0, 0);
    }
  }
}

.legend-window .su-panel-header {
  font-size: 18px;
  padding-top: 5px;
  padding-bottom: 5px;
}

.closeIcon {
  position: absolute;
  right: 15px;
  color: #00f7ff;
}

.minIcon {
  position: absolute;
  right: 40px;
  color: #00f7ff;
}

.el-input {
  --el-input-text-color: #fbfbfb !important;
  --el-input-hover-border: #369ef0 !important;
  --el-input-focus-border: #369ef0 !important;

  --el-input-border-color: rgb(253 253 253 / 75%) !important;

  --el-input-bg-color: #ffffff00 !important;
  --el-input-focus-border-color: #369ef0 !important;
}

.el-select {
  --el-select-input-focus-border-color: #369ef0 !important;
}

.el-select__popper.el-popper[role="tooltip"] {
  background: rgba(16, 27, 55, 0.8) !important;
  border: 1px solid #ffffff !important;
  box-shadow: var(--el-box-shadow-light) !important;
  color: white !important;
}

.el-select-dropdown__item.selected {
  color: #369ef0 !important;
  font-weight: bold;
}

.el-select-dropdown__item {
  color: #ffffff !important;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #edeff200 !important;
}

.whiteBtn {
  --el-button-hover-bg-color: rgb(37 250 200 / 94%) !important;
  background-color: #409eff03 !important;
  border-color: white !important;
  color: white !important;
}

.el-input-number__increase,
.el-input-number__decrease {
  background: #f5f7fa00 !important;
  color: #fafbff !important;
}

.el-select .el-input .el-select__caret {
  color: #fafbff !important;
}

.q3d-panel {
  position: fixed;
  right: 0rem;
  // left: 15.11111rem;
  z-index: 1000;
  // bottom: 10rem;
}

.el-table__body-wrapper::-webkit-scrollbar {
  height: 6px !important;
  width: 6px !important;
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #0d233800 !important;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ccc !important;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf !important;
}

.el-table {
  background-color: #ffffff00 !important;
  --el-table-bg-color: #ffffff00 !important;
  --el-table-row-hover-bg-color: #369ef0 !important;

  tr {
    background-color: #ffffff00 !important;
    color: white !important;
  }
}

.el-table th.el-table__cell {
  user-select: none;
  background-color: #ffffff00;
}

.el-table__body tr.current-row > td {
  background-color: #369ef0 !important;
}

.ellipse17 {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  left: 5px;
  top: 20px;
  position: flex;
  background: #0d99ff;
  margin-right: 1em !important;
}
.layer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .button-group {
    display: flex;
    gap: 8px;

    .el-button {
      color: #a0deff;
      padding: 0;

      &:hover {
        color: #ffffff;
      }
    }
  }
}
</style>
